import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, MessageCircle, Crown, MapPin, Users } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { LikeProfile } from '@/types/messaging';
import { usePremiumStore } from '@/stores/premiumStore';
import { theme } from '@/constants/theme';
import { useSafeAsync } from '@/hooks/useSafeAsync';
import { formatTimeAgo } from '@/utils/dateUtils';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

interface AnimatedLikeCardProps {
  profile: LikeProfile;
  onLikeBack: (userId: string) => Promise<boolean>;
  onMessage: (userId: string) => Promise<void>;
  onPress: (profile: LikeProfile) => void;
}

const triggerHaptic = {
  light: () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  },
  medium: () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  },
  success: () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  },
};

export default function AnimatedLikeCard({
  profile,
  onLikeBack,
  onMessage,
  onPress,
}: AnimatedLikeCardProps) {
  const [isLiking, setIsLiking] = useState(false);
  const [showMatch, setShowMatch] = useState(false);
  const [isMessaging, setIsMessaging] = useState(false);
  const { safeAsync, safeSetTimeout, safeSetState } = useSafeAsync();

  // Premium store integration
  const { isPremium, hasFeature } = usePremiumStore();

  const scale = useSharedValue(1);
  const heartScale = useSharedValue(0);
  const heartOpacity = useSharedValue(0);
  const matchScale = useSharedValue(0);
  const matchOpacity = useSharedValue(0);

  const handlePress = () => {
    triggerHaptic.light();
    scale.value = withSpring(0.95, { duration: 150 }, () => {
      scale.value = withSpring(1, { duration: 150 });
    });
    onPress(profile);
  };

  const handleLikeBack = async () => {
    if (isLiking) return;

    safeSetState(setIsLiking, true);
    triggerHaptic.medium();

    // Animate heart
    heartScale.value = withSpring(1.2, { duration: 300 });
    heartOpacity.value = withTiming(1, { duration: 200 });

    await safeAsync(
      () => onLikeBack(profile.id),
      (isMatch) => {
        if (isMatch) {
          // Show match animation
          safeSetState(setShowMatch, true);
          triggerHaptic.success();

          matchScale.value = withSpring(1, { duration: 500 });
          matchOpacity.value = withTiming(1, { duration: 300 });

          // Hide match animation after 2 seconds
          safeSetTimeout(() => {
            matchOpacity.value = withTiming(0, { duration: 300 });
            matchScale.value = withTiming(0, { duration: 300 });
            runOnJS(() => {
              safeSetState(setShowMatch, false);
            })();
          }, 2000);
        }

        // Reset heart animation
        safeSetTimeout(() => {
          heartOpacity.value = withTiming(0, { duration: 300 });
          heartScale.value = withTiming(0, { duration: 300 });
        }, 1000);

        safeSetState(setIsLiking, false);
      },
      (error) => {
        console.error('Error liking back:', error);
        safeSetState(setIsLiking, false);
      }
    );
  };

  const handleMessage = async () => {
    if (isMessaging) return;

    safeSetState(setIsMessaging, true);
    triggerHaptic.medium();

    try {
      await onMessage(profile.id);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      safeSetState(setIsMessaging, false);
    }
  };

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const heartAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
    opacity: heartOpacity.value,
  }));

  const matchAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: matchScale.value }],
    opacity: matchOpacity.value,
  }));

  // Using the utility function for consistent date handling

  return (
    <Animated.View style={[styles.container, cardAnimatedStyle]}>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.9}>
        <View style={styles.card}>
          <Image source={{ uri: profile.photos[0] }} style={styles.image} />
          
          {/* Premium Badge */}
          {profile.isPremium && (
            <View style={styles.premiumBadge}>
              <Crown size={12} color="#FFD700" />
            </View>
          )}

          {/* Gradient Overlay */}
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.gradient}
          />

          {/* Profile Info */}
          <View style={styles.info}>
            <Text style={styles.name}>{profile.name}, {profile.age}</Text>
            
            {profile.distance && (
              <View style={styles.distanceContainer}>
                <MapPin size={12} color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.distance}>{profile.distance} km away</Text>
              </View>
            )}
            
            {profile.mutualFriends && profile.mutualFriends > 0 && (
              <View style={styles.mutualContainer}>
                <Users size={12} color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.mutual}>
                  {profile.mutualFriends} mutual friend{profile.mutualFriends > 1 ? 's' : ''}
                </Text>
              </View>
            )}
            
            <Text style={styles.timeAgo}>{formatTimeAgo(profile.likedAt)}</Text>
            
            {/* Action Button */}
            {profile.isMatch ? (
              <TouchableOpacity
                style={[styles.messageButton, isMessaging && styles.messageButtonDisabled]}
                onPress={handleMessage}
                disabled={isLiking || isMessaging}
              >
                <MessageCircle size={16} color="white" />
                <Text style={styles.messageButtonText}>
                  {isMessaging ? 'Opening...' : 'Message'}
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.likeButton, isLiking && styles.likeButtonDisabled]}
                onPress={handleLikeBack}
                disabled={isLiking || isMessaging}
              >
                <Heart size={16} color={theme.colors.like} />
                <Text style={styles.likeButtonText}>
                  {isLiking ? 'Liking...' : 'Like Back'}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Heart Animation Overlay */}
          <Animated.View style={[styles.heartOverlay, heartAnimatedStyle]}>
            <Heart size={60} color={theme.colors.like} fill={theme.colors.like} />
          </Animated.View>

          {/* Match Animation Overlay */}
          {showMatch && (
            <Animated.View style={[styles.matchOverlay, matchAnimatedStyle]}>
              <View style={styles.matchContent}>
                <Text style={styles.matchText}>It's a Match! 🎉</Text>
              </View>
            </Animated.View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    marginBottom: 16,
  },
  card: {
    width: '100%',
    height: CARD_WIDTH * 1.3,
    borderRadius: 16,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  premiumBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 6,
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  info: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  distance: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 4,
  },
  mutualContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  mutual: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 4,
  },
  timeAgo: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 8,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  messageButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginLeft: 4,
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  likeButtonDisabled: {
    opacity: 0.6,
  },
  likeButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.like,
    marginLeft: 4,
  },
  messageButtonDisabled: {
    opacity: 0.6,
  },
  heartOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -30,
    marginLeft: -30,
    zIndex: 10,
  },
  matchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  matchContent: {
    alignItems: 'center',
  },
  matchText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: 'white',
    textAlign: 'center',
  },
});
