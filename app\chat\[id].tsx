import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import ChatScreen from '@/components/messaging/ChatScreen';
import { Message, User, Conversation } from '@/types/messaging';
import { useMessagesStore } from '@/stores/messagesStore';

// Mock data - replace with real data from your backend
const CURRENT_USER: User = {
  id: 'current-user',
  name: 'You',
  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  isOnline: true,
};

const MOCK_CONVERSATION: Conversation = {
  id: 'conv-1',
  participants: [
    CURRENT_USER,
    {
      id: 'user-1',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      isOnline: true,
    }
  ],
  lastMessage: {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! Thanks for the like 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
  unreadCount: 0,
  isTyping: false,
  typingUsers: [],
};

const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! How are you doing?',
    type: 'text',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-2',
    senderId: 'current-user',
    receiverId: 'user-1',
    content: 'I\'m doing great! Thanks for asking 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-3',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'That\'s awesome! Want to grab coffee this weekend?',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
];

// Helper functions to get user data for mock conversations
const getUserNameForId = (userId: string): string => {
  const userNames: { [key: string]: string } = {
    '1': 'Emma',
    '2': 'Sophia',
    '3': 'Olivia',
    '4': 'Ava',
    '5': 'Isabella',
    '6': 'Mia',
    '7': 'Charlotte',
    '8': 'Amelia',
  };
  return userNames[userId] || `User ${userId}`;
};

const getUserAvatarForId = (userId: string): string => {
  const userAvatars: { [key: string]: string } = {
    '1': 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    '2': 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
    '3': 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
    '4': 'https://images.pexels.com/photos/1858175/pexels-photo-1858175.jpeg?auto=compress&cs=tinysrgb&w=400',
    '5': 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
    '6': 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=400',
    '7': 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    '8': 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
  };
  return userAvatars[userId] || 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400';
};

export default function ChatPage() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);

  const {
    conversations,
    currentUser,
    getMessagesForConversation,
    sendMessage,
    markAsRead,
    findConversationByParticipant,
    fetchConversations,
  } = useMessagesStore();

  useEffect(() => {
    const loadConversation = async () => {
      if (typeof id === 'string') {
        try {
          console.log('Loading conversation for user ID:', id);

          // Ensure conversations are loaded
          if (conversations.length === 0) {
            console.log('Fetching conversations...');
            await fetchConversations();
          }

          // Wait a bit for state to update after fetchConversations
          await new Promise(resolve => setTimeout(resolve, 100));

          // Find conversation by participant ID
          console.log('Looking for conversation with participant:', id);
          let foundConversation = findConversationByParticipant(id);

          // If not found, try again after a short delay (for timing issues)
          if (!foundConversation) {
            console.log('Conversation not found, retrying after delay...');
            await new Promise(resolve => setTimeout(resolve, 200));
            foundConversation = findConversationByParticipant(id);
          }

          if (foundConversation) {
            console.log('Found conversation:', foundConversation.id);
            setConversation(foundConversation);

            // Load messages for this conversation
            const conversationMessages = getMessagesForConversation(foundConversation.id);
            console.log('Loaded messages:', conversationMessages.length);
            setMessages(conversationMessages);
          } else {
            console.log('Conversation still not found, using mock data for user:', id);

            // Create a dynamic mock conversation for this specific user
            const mockConversation = {
              ...MOCK_CONVERSATION,
              id: `mock-conv-${id}`,
              participants: [
                {
                  id: 'current-user',
                  name: 'You',
                  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
                  isOnline: true,
                },
                {
                  id: id,
                  name: getUserNameForId(id),
                  avatar: getUserAvatarForId(id),
                  isOnline: Math.random() > 0.5,
                }
              ]
            };

            setConversation(mockConversation);
            setMessages(MOCK_MESSAGES);
          }
        } catch (error) {
          console.error('Error loading conversation:', error);
          // Use mock data as fallback
          setConversation(MOCK_CONVERSATION);
          setMessages(MOCK_MESSAGES);
        }
      }
    };

    loadConversation();
  }, [id, conversations]);

  const handleSendMessage = async (content: string, type: 'text' | 'image' | 'file') => {
    if (!conversation || !currentUser) return;

    try {
      await sendMessage(conversation.id, content, type);

      // Refresh messages
      const updatedMessages = getMessagesForConversation(conversation.id);
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleStartCall = (type: 'audio' | 'video') => {
    // TODO: Implement call functionality
    console.log('Starting call:', type);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleStartTyping = () => {
    // TODO: Implement typing indicators
  };

  const handleStopTyping = () => {
    // TODO: Implement typing indicators
  };

  const handleMarkAsRead = (messageId: string) => {
    if (conversation) {
      markAsRead(conversation.id, messageId);
    }
  };

  if (!conversation) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Loading conversation...</Text>
      </View>
    );
  }

  if (!currentUser) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Setting up user...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ChatScreen
        conversation={conversation}
        messages={messages}
        currentUser={currentUser}
        onSendMessage={handleSendMessage}
        onStartCall={handleStartCall}
        onGoBack={handleGoBack}
        onStartTyping={handleStartTyping}
        onStopTyping={handleStopTyping}
        onMarkAsRead={handleMarkAsRead}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});