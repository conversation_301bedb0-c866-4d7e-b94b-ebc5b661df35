export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  age: number;
  dateOfBirth: Date;
  bio: string;
  photos: ProfilePhoto[];
  location: {
    city: string;
    state: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  occupation: string;
  education: string;
  height?: number; // in cm
  interests: string[];
  languages: string[];
  relationshipGoals: 'casual' | 'serious' | 'friendship' | 'unsure';
  
  // Profile status
  verified: boolean;
  isPremium: boolean;
  isOnline: boolean;
  lastSeen?: Date;
  profileCompletion: number; // 0-100
  verificationStatus: VerificationStatus;
  blockedUsers: BlockedUser[];
  reportedUsers: ReportedUser[];
  
  // Social media
  socialMedia?: {
    instagram?: string;
    spotify?: string;
    linkedin?: string;
  };
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface ProfilePhoto {
  id: string;
  url: string;
  order: number;
  isMain: boolean;
  uploadedAt: Date;
  width?: number;
  height?: number;
  verificationStatus?: 'pending' | 'verified' | 'rejected' | 'none';
  editedAt?: Date;
}

export interface DatingPreferences {
  ageRange: {
    min: number;
    max: number;
  };
  maxDistance: number; // in km
  genderPreference: 'men' | 'women' | 'everyone';
  showMe: 'men' | 'women' | 'everyone';
  dealBreakers: string[];
  mustHaves: string[];
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  showDistance: boolean;
  showAge: boolean;
  showLastSeen: boolean;
  allowMessagesFrom: 'everyone' | 'matches' | 'premium';
  showOnlineStatus: boolean;
  incognitoMode: boolean;
}

export interface NotificationSettings {
  pushNotifications: boolean;
  emailNotifications: boolean;
  newMatches: boolean;
  newMessages: boolean;
  likes: boolean;
  superLikes: boolean;
  promotions: boolean;
  tips: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string; // HH:MM format
  };
}

export interface AccountSettings {
  twoFactorAuth: boolean;
  loginAlerts: boolean;
  dataSharing: boolean;
  analytics: boolean;
  locationServices: boolean;
  autoRenewal: boolean;
  emailVerified: boolean;
  phoneVerified: boolean;
  backupEmail?: string;
  recoveryPhone?: string;
  sessionTimeout: number; // in minutes
  deviceManagement: boolean;
}

export interface AppPreferences {
  language: string;
  theme: 'light' | 'dark' | 'system';
  units: 'metric' | 'imperial';
  currency: string;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  autoPlayVideos: boolean;
  reducedMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
}

export interface VerificationStatus {
  identity: {
    verified: boolean;
    verifiedAt?: Date;
    method?: 'id_document' | 'selfie' | 'video_call';
    status: 'pending' | 'approved' | 'rejected' | 'not_started';
  };
  phone: {
    verified: boolean;
    verifiedAt?: Date;
    number?: string;
    status: 'pending' | 'approved' | 'rejected' | 'not_started';
  };
  email: {
    verified: boolean;
    verifiedAt?: Date;
    status: 'pending' | 'approved' | 'rejected' | 'not_started';
  };
  photo: {
    verified: boolean;
    verifiedAt?: Date;
    status: 'pending' | 'approved' | 'rejected' | 'not_started';
  };
}

export interface BlockedUser {
  id: string;
  name: string;
  photo?: string;
  blockedAt: Date;
  reason?: string;
}

export interface ReportedUser {
  id: string;
  name: string;
  photo?: string;
  reportedAt: Date;
  reason: string;
  status: 'pending' | 'resolved' | 'dismissed';
}

export interface ProfileSettings {
  dating: DatingPreferences;
  privacy: PrivacySettings;
  notifications: NotificationSettings;
  account: AccountSettings;
  app: AppPreferences;
}

export interface SecuritySettings {
  loginHistory: LoginSession[];
  activeDevices: Device[];
  trustedDevices: Device[];
  suspiciousActivity: SecurityEvent[];
}

export interface LoginSession {
  id: string;
  deviceInfo: string;
  location: string;
  ipAddress: string;
  loginAt: Date;
  isActive: boolean;
  userAgent: string;
}

export interface Device {
  id: string;
  name: string;
  type: 'mobile' | 'desktop' | 'tablet';
  os: string;
  browser?: string;
  lastUsed: Date;
  isTrusted: boolean;
  location: string;
}

export interface SecurityEvent {
  id: string;
  type: 'failed_login' | 'password_change' | 'email_change' | 'suspicious_location';
  description: string;
  timestamp: Date;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high';
}

export interface ProfileValidation {
  isValid: boolean;
  errors: {
    [key: string]: string[];
  };
  warnings: {
    [key: string]: string[];
  };
}

export interface ProfileUpdateRequest {
  field: keyof UserProfile;
  value: any;
  validate?: boolean;
}

export interface PhotoUploadRequest {
  uri: string;
  type: string;
  name: string;
  isMain?: boolean;
  order?: number;
  id?: string;
}

export interface ProfileAnalytics {
  profileViews: number;
  likesReceived: number;
  matchesCount: number;
  messagesReceived: number;
  profileViewsThisWeek: number;
  popularPhotos: string[]; // photo IDs
  peakActivityHours: number[];
  averageResponseTime: number; // in minutes
}

// Interest categories for suggestions
export const INTEREST_CATEGORIES = {
  SPORTS: ['Football', 'Basketball', 'Tennis', 'Swimming', 'Running', 'Yoga', 'Gym', 'Hiking', 'Cycling', 'Rock Climbing'],
  ARTS: ['Photography', 'Painting', 'Music', 'Dancing', 'Theater', 'Writing', 'Reading', 'Poetry', 'Sculpture'],
  FOOD: ['Cooking', 'Wine Tasting', 'Coffee', 'Baking', 'Vegetarian', 'Vegan', 'Food Blogging', 'Restaurant Hopping'],
  TRAVEL: ['Backpacking', 'Road Trips', 'Beach Vacations', 'City Breaks', 'Adventure Travel', 'Cultural Tourism'],
  TECHNOLOGY: ['Gaming', 'Programming', 'AI/ML', 'Cryptocurrency', 'Gadgets', 'VR/AR', 'Robotics'],
  LIFESTYLE: ['Fashion', 'Meditation', 'Spirituality', 'Volunteering', 'Environmental Causes', 'Minimalism'],
  ENTERTAINMENT: ['Movies', 'TV Shows', 'Podcasts', 'Stand-up Comedy', 'Concerts', 'Festivals', 'Board Games'],
  LEARNING: ['Languages', 'History', 'Science', 'Philosophy', 'Psychology', 'Astronomy', 'Archaeology'],
} as const;

export type InterestCategory = keyof typeof INTEREST_CATEGORIES;

// Validation rules
export const PROFILE_VALIDATION_RULES = {
  bio: {
    minLength: 10,
    maxLength: 500,
  },
  interests: {
    minCount: 3,
    maxCount: 10,
  },
  photos: {
    minCount: 2,
    maxCount: 9,
    maxSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  age: {
    min: 18,
    max: 100,
  },
  name: {
    minLength: 2,
    maxLength: 50,
  },
} as const;

// Profile completion weights
export const PROFILE_COMPLETION_WEIGHTS = {
  photos: 30,
  bio: 20,
  interests: 15,
  occupation: 10,
  education: 10,
  location: 10,
  socialMedia: 5,
} as const;
