import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Send, Paperclip, Mic, Phone, Video, MoveVertical as MoreVertical, Smile, Camera, Search, Plus } from 'lucide-react-native';
import { Message, User, Conversation } from '@/types/messaging';
import EnhancedMessageBubble from './EnhancedMessageBubble';
import TranslationButton from './TranslationButton';
import TypingIndicator from './TypingIndicator';
import EmojiPicker from './EmojiPicker';
import FileAttachmentModal from './FileAttachmentModal';
import MessageReactions from './MessageReactions';
import MessageSearch from './MessageSearch';
import MessageSkeleton from './MessageSkeleton';
import { useMessagesStore } from '@/stores/messagesStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

interface ChatScreenProps {
  conversation: Conversation;
  messages: Message[];
  currentUser: User;
  onSendMessage: (content: string, type: 'text' | 'image' | 'file') => void;
  onStartCall: (type: 'audio' | 'video') => void;
  onGoBack: () => void;
  onStartTyping: () => void;
  onStopTyping: () => void;
  onMarkAsRead: (messageId: string) => void;
}

export default function ChatScreen({
  conversation,
  messages,
  currentUser,
  onSendMessage,
  onStartCall,
  onGoBack,
  onStartTyping,
  onStopTyping,
  onMarkAsRead
}: ChatScreenProps) {
  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const [showMessageSearch, setShowMessageSearch] = useState(false);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const {
    messageReactions,
    addReaction,
    removeReaction,
    isConnected,
    typingUsers
  } = useMessagesStore();

  const otherUser = conversation.participants.find(p => p.id !== currentUser.id) || conversation.participants[0];
  const isTyping = typingUsers[conversation.id]?.includes(otherUser.id) || false;

  useEffect(() => {
    // Mark messages as read when they come into view
    const unreadMessages = messages.filter(m => 
      m.receiverId === currentUser.id && m.status !== 'read'
    );
    unreadMessages.forEach(message => {
      onMarkAsRead(message.id);
    });
  }, [messages, currentUser.id, onMarkAsRead]);

  const handleSendMessage = async () => {
    if (messageText.trim()) {
      // Haptic feedback
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      const content = messageText.trim();
      setMessageText('');
      onStopTyping();

      try {
        await onSendMessage(content, 'text');

        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        // Restore message text on error
        setMessageText(content);
        Alert.alert('Error', 'Failed to send message. Please try again.');
      }
    }
  };

  const handleTextChange = (text: string) => {
    setMessageText(text);
    
    if (text.length > 0) {
      onStartTyping();
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        onStopTyping();
      }, 1000);
    } else {
      onStopTyping();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessageText(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  const handleVoiceRecord = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (isRecording) {
      // Stop recording logic
      setIsRecording(false);
    } else {
      // Start recording logic
      setIsRecording(true);
    }
  };

  const handleMessageLongPress = (messageId: string) => {
    setSelectedMessageId(messageId);
    setShowReactionPicker(true);
  };

  const handleReactionSelect = (emoji: string) => {
    if (selectedMessageId && currentUser) {
      addReaction(selectedMessageId, emoji);
      setShowReactionPicker(false);
      setSelectedMessageId(null);
    }
  };

  const handleSearchPress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setShowMessageSearch(true);
  };

  const handleMessageSelect = (conversationId: string, messageId: string) => {
    // Navigate to specific message
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      flatListRef.current?.scrollToIndex({
        index: messageIndex,
        animated: true,
        viewPosition: 0.5
      });
    }
    // Close search modal
    setShowMessageSearch(false);

    // Haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleFileSelect = async (file: any, type: 'image' | 'file') => {
    try {
      setIsLoading(true);
      // Handle file upload logic here
      await onSendMessage(file.name || 'File', type);
      setShowAttachmentModal(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to send file. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderMessage = ({ item, index }: { item: Message; index: number }) => {
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const showAvatar = !previousMessage || previousMessage.senderId !== item.senderId;
    const isConsecutive = previousMessage && 
      previousMessage.senderId === item.senderId &&
      (item.timestamp.getTime() - previousMessage.timestamp.getTime()) < 60000; // 1 minute

    return (
      <EnhancedMessageBubble
        message={item}
        isOwn={item.senderId === currentUser.id}
        showAvatar={showAvatar}
        isConsecutive={isConsecutive ?? false}
        senderAvatar={item.senderId === currentUser.id ? currentUser.avatar : otherUser.avatar}
        onLongPress={() => handleMessageLongPress(item.id)}
      />
    );
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView 
          style={styles.keyboardView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={onGoBack}>
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
            
            <View style={styles.userInfo}>
              <Image source={{ uri: otherUser.avatar }} style={styles.headerAvatar} />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{otherUser.name}</Text>
                <Text style={styles.userStatus}>
                  {isTyping ? 'typing...' : otherUser.isOnline ? 'Online' : 'Last seen recently'}
                </Text>
              </View>
            </View>

            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleSearchPress}
              >
                <Search size={20} color="white" />
              </TouchableOpacity>
              <TranslationButton />
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => onStartCall('audio')}
              >
                <Phone size={20} color="white" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => onStartCall('video')}
              >
                <Video size={20} color="white" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton}>
                <MoreVertical size={20} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Messages */}
          <View style={styles.messagesContainer}>
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessage}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.messagesList}
              onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
              inverted={false}
            />
          </View>

          {/* Typing Indicator */}
          <TypingIndicator
            isVisible={isTyping}
            userName={otherUser.name}
          />

          {/* Input Area */}
          <View style={styles.inputContainer}>
            <View style={styles.inputRow}>
              <TouchableOpacity 
                style={styles.attachButton}
                onPress={() => setShowAttachmentModal(true)}
              >
                <Paperclip size={20} color="#8B5CF6" />
              </TouchableOpacity>

              <View style={styles.textInputContainer}>
                <TextInput
                  style={styles.textInput}
                  placeholder="Type a message..."
                  placeholderTextColor="#9CA3AF"
                  value={messageText}
                  onChangeText={handleTextChange}
                  multiline
                  maxLength={1000}
                />
                <TouchableOpacity 
                  style={styles.emojiButton}
                  onPress={() => setShowEmojiPicker(!showEmojiPicker)}
                >
                  <Smile size={20} color="#8B5CF6" />
                </TouchableOpacity>
              </View>

              {messageText.trim() ? (
                <TouchableOpacity style={styles.sendButton} onPress={handleSendMessage}>
                  <Send size={20} color="white" />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity 
                  style={[styles.voiceButton, isRecording && styles.voiceButtonRecording]}
                  onPress={handleVoiceRecord}
                >
                  <Mic size={20} color={isRecording ? "white" : "#8B5CF6"} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Emoji Picker */}
          {showEmojiPicker && (
            <EmojiPicker
              onEmojiSelect={handleEmojiSelect}
              onClose={() => setShowEmojiPicker(false)}
            />
          )}

          {/* File Attachment Modal */}
          <FileAttachmentModal
            visible={showAttachmentModal}
            onClose={() => setShowAttachmentModal(false)}
            onFileSelect={handleFileSelect}
          />

          {/* Message Reactions Modal */}
          <MessageReactions
            visible={showReactionPicker}
            onClose={() => setShowReactionPicker(false)}
            onReactionSelect={handleReactionSelect}
            messageId={selectedMessageId || ''}
          />

          {/* Message Search Modal */}
          <MessageSearch
            visible={showMessageSearch}
            onClose={() => setShowMessageSearch(false)}
            messages={{ [conversation.id]: messages }}
            conversations={[conversation]}
            users={{ [currentUser.id]: currentUser, [otherUser.id]: otherUser }}
            onMessageSelect={handleMessageSelect}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  backButton: {
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  userStatus: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  messagesList: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  typingContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  typingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#8B5CF6',
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
  },
  attachButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
    maxHeight: 80,
  },
  emojiButton: {
    marginLeft: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#8B5CF6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButtonRecording: {
    backgroundColor: '#FF4458',
  },
});