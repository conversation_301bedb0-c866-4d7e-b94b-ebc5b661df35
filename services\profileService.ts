import { UserProfile, ProfileSettings, PhotoUploadRequest, ProfileAnalytics } from '@/types/profile';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class ProfileService {
  private static instance: ProfileService;
  private readonly PROFILE_KEY = 'user_profile';
  private readonly SETTINGS_KEY = 'profile_settings';
  private readonly ANALYTICS_KEY = 'profile_analytics';

  public static getInstance(): ProfileService {
    if (!ProfileService.instance) {
      ProfileService.instance = new ProfileService();
    }
    return ProfileService.instance;
  }

  /**
   * Load user profile from storage or API
   */
  public async loadProfile(): Promise<UserProfile | null> {
    try {
      // In production, this would be an API call
      const stored = await AsyncStorage.getItem(this.PROFILE_KEY);
      if (stored) {
        const profile = JSON.parse(stored);
        // Convert date strings back to Date objects
        profile.dateOfBirth = new Date(profile.dateOfBirth);
        profile.createdAt = new Date(profile.createdAt);
        profile.updatedAt = new Date(profile.updatedAt);
        if (profile.lastSeen) {
          profile.lastSeen = new Date(profile.lastSeen);
        }
        profile.photos = profile.photos.map((photo: any) => ({
          ...photo,
          uploadedAt: new Date(photo.uploadedAt),
        }));
        return profile;
      }
      return null;
    } catch (error) {
      console.error('Failed to load profile:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  public async updateProfile(profile: UserProfile): Promise<void> {
    try {
      // In production, this would be an API call
      await AsyncStorage.setItem(this.PROFILE_KEY, JSON.stringify(profile));
      console.log('Profile updated successfully');
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  /**
   * Load profile settings
   */
  public async loadSettings(): Promise<ProfileSettings | null> {
    try {
      const stored = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
      return null;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return null;
    }
  }

  /**
   * Update profile settings
   */
  public async updateSettings(settings: ProfileSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
      console.log('Settings updated successfully');
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw new Error('Failed to update settings');
    }
  }

  /**
   * Upload photo with compression and optimization
   */
  public async uploadPhoto(photoRequest: PhotoUploadRequest): Promise<string> {
    try {
      // Compress and optimize image
      const optimizedImage = await this.optimizeImage(photoRequest.uri);
      
      // In production, this would upload to cloud storage (AWS S3, Cloudinary, etc.)
      // For demo, we'll simulate the upload and return a mock ID
      const photoId = `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Photo uploaded successfully:', photoId);
      return photoId;
    } catch (error) {
      console.error('Failed to upload photo:', error);
      throw new Error('Failed to upload photo');
    }
  }

  /**
   * Delete photo
   */
  public async deletePhoto(photoId: string): Promise<void> {
    try {
      // In production, this would delete from cloud storage
      console.log('Photo deleted successfully:', photoId);
    } catch (error) {
      console.error('Failed to delete photo:', error);
      throw new Error('Failed to delete photo');
    }
  }

  /**
   * Optimize image for upload
   * Note: In production, you would install expo-image-manipulator for actual image optimization
   */
  private async optimizeImage(uri: string): Promise<string> {
    try {
      // For now, return the original URI
      // In production, you would use expo-image-manipulator:
      // const result = await ImageManipulator.manipulateAsync(uri, [...], {...});
      console.log('Image optimization would happen here in production');
      return uri;
    } catch (error) {
      console.error('Failed to optimize image:', error);
      return uri; // Return original if optimization fails
    }
  }

  /**
   * Request camera/gallery permissions
   */
  public async requestPermissions(): Promise<boolean> {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      return cameraPermission.status === 'granted' && galleryPermission.status === 'granted';
    } catch (error) {
      console.error('Failed to request permissions:', error);
      return false;
    }
  }

  /**
   * Pick image from gallery
   */
  public async pickImageFromGallery(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Camera and gallery permissions are required');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 5], // Dating app photo aspect ratio
        quality: 0.9,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        return result.assets[0].uri;
      }
      return null;
    } catch (error) {
      console.error('Failed to pick image from gallery:', error);
      throw error;
    }
  }

  /**
   * Take photo with camera
   */
  public async takePhotoWithCamera(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Camera and gallery permissions are required');
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 5],
        quality: 0.9,
      });

      if (!result.canceled && result.assets[0]) {
        return result.assets[0].uri;
      }
      return null;
    } catch (error) {
      console.error('Failed to take photo with camera:', error);
      throw error;
    }
  }

  /**
   * Edit photo with cropping and filters
   * Note: In production, you would use expo-image-manipulator for actual image editing
   */
  public async editPhoto(uri: string, edits: {
    crop?: { x: number; y: number; width: number; height: number };
    rotate?: number;
    brightness?: number;
    contrast?: number;
    saturation?: number;
  }): Promise<string> {
    try {
      // In production, you would use expo-image-manipulator:
      // const result = await ImageManipulator.manipulateAsync(uri, [
      //   { crop: edits.crop },
      //   { rotate: edits.rotate || 0 },
      // ], {
      //   compress: 0.9,
      //   format: ImageManipulator.SaveFormat.JPEG,
      // });

      // For demo purposes, simulate processing and return original URI
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Photo editing would happen here in production', edits);
      return uri;
    } catch (error) {
      console.error('Failed to edit photo:', error);
      throw new Error('Failed to edit photo');
    }
  }

  /**
   * Verify photo for authenticity
   */
  public async verifyPhoto(photoId: string): Promise<'pending' | 'verified' | 'rejected'> {
    try {
      // In production, this would submit to a verification service
      // For demo, simulate verification process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate random verification result for demo
      const results: ('verified' | 'rejected')[] = ['verified', 'verified', 'verified', 'rejected'];
      const result = results[Math.floor(Math.random() * results.length)];

      console.log(`Photo ${photoId} verification result: ${result}`);
      return result;
    } catch (error) {
      console.error('Failed to verify photo:', error);
      return 'rejected';
    }
  }

  /**
   * Load profile analytics
   */
  public async loadAnalytics(): Promise<ProfileAnalytics | null> {
    try {
      // In production, this would be an API call
      const stored = await AsyncStorage.getItem(this.ANALYTICS_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Return mock analytics for demo
      return {
        profileViews: 156,
        likesReceived: 23,
        matchesCount: 8,
        messagesReceived: 45,
        profileViewsThisWeek: 34,
        popularPhotos: ['photo-1', 'photo-2'],
        peakActivityHours: [19, 20, 21],
        averageResponseTime: 45,
      };
    } catch (error) {
      console.error('Failed to load analytics:', error);
      return null;
    }
  }

  /**
   * Validate profile data
   */
  public validateProfileData(profile: Partial<UserProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate required fields
    if (!profile.name || profile.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!profile.bio || profile.bio.trim().length < 10) {
      errors.push('Bio must be at least 10 characters long');
    }

    if (profile.bio && profile.bio.length > 500) {
      errors.push('Bio must be less than 500 characters');
    }

    if (!profile.photos || profile.photos.length < 2) {
      errors.push('At least 2 photos are required');
    }

    if (profile.age && (profile.age < 18 || profile.age > 100)) {
      errors.push('Age must be between 18 and 100');
    }

    if (!profile.interests || profile.interests.length < 3) {
      errors.push('At least 3 interests are required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get interest suggestions based on current interests
   */
  public getInterestSuggestions(currentInterests: string[]): string[] {
    const allInterests = [
      'Travel', 'Photography', 'Hiking', 'Coffee', 'Music', 'Dancing', 'Cooking',
      'Reading', 'Movies', 'Fitness', 'Yoga', 'Art', 'Wine', 'Gaming', 'Sports',
      'Fashion', 'Technology', 'Volunteering', 'Meditation', 'Writing', 'Theater',
      'Cycling', 'Swimming', 'Running', 'Climbing', 'Skiing', 'Surfing', 'Camping',
      'Gardening', 'Pets', 'Languages', 'History', 'Science', 'Philosophy',
    ];

    // Filter out current interests and return suggestions
    return allInterests
      .filter(interest => !currentInterests.includes(interest))
      .slice(0, 10);
  }

  /**
   * Calculate profile completion percentage
   */
  public calculateProfileCompletion(profile: UserProfile): number {
    let score = 0;
    const maxScore = 100;

    // Photos (30 points)
    if (profile.photos && profile.photos.length >= 2) {
      score += 30;
    } else if (profile.photos && profile.photos.length === 1) {
      score += 15;
    }

    // Bio (20 points)
    if (profile.bio && profile.bio.length >= 50) {
      score += 20;
    } else if (profile.bio && profile.bio.length >= 10) {
      score += 10;
    }

    // Interests (15 points)
    if (profile.interests && profile.interests.length >= 5) {
      score += 15;
    } else if (profile.interests && profile.interests.length >= 3) {
      score += 10;
    }

    // Basic info (35 points total)
    if (profile.occupation) score += 10;
    if (profile.education) score += 10;
    if (profile.location && profile.location.city) score += 10;
    if (profile.socialMedia && Object.keys(profile.socialMedia).length > 0) score += 5;

    return Math.min(score, maxScore);
  }

  /**
   * Clear all profile data (for account deletion)
   */
  public async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(this.PROFILE_KEY),
        AsyncStorage.removeItem(this.SETTINGS_KEY),
        AsyncStorage.removeItem(this.ANALYTICS_KEY),
      ]);
      console.log('All profile data cleared');
    } catch (error) {
      console.error('Failed to clear profile data:', error);
      throw new Error('Failed to clear profile data');
    }
  }
}

// Export singleton instance
export const profileService = ProfileService.getInstance();
