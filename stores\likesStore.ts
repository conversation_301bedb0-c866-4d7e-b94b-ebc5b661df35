import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Like, Match, LikeProfile } from '@/types/messaging';
import { likesService } from '@/services/likesService';
import { safeParseDate } from '@/utils/dateUtils';
import { likesCacheService } from '@/services/likesCache';
import { likesNotificationService } from '@/services/likesNotifications';
import { getRealTimeLikesManager } from '@/services/realTimeLikes';
import { usePremiumStore } from '@/stores/premiumStore';
import { FREE_USER_LIMITS } from '@/types/premium';

interface LikesState {
  // Data
  receivedLikes: LikeProfile[];
  sentLikes: Like[];
  matches: Match[];

  // UI State
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  isOnline: boolean;

  // Pagination
  hasMoreLikes: boolean;
  hasMoreMatches: boolean;
  likesPage: number;
  matchesPage: number;

  // Premium feature tracking
  dailyLikesUsed: number;
  dailySuperLikesUsed: number;
  lastResetDate: string;

  // Actions
  fetchReceivedLikes: (refresh?: boolean) => Promise<void>;
  fetchMatches: (refresh?: boolean) => Promise<void>;
  sendLike: (userId: string, type: 'like' | 'superlike') => Promise<boolean>;
  sendPass: (userId: string) => Promise<void>;
  likeBack: (userId: string) => Promise<boolean>;

  // Premium feature checks
  canSendLike: () => { canSend: boolean; reason?: string };
  canSendSuperLike: () => { canSend: boolean; reason?: string };
  canViewWhoLikesYou: () => boolean;
  getPremiumStatus: () => {
    isPremium: boolean;
    canViewLikes: boolean;
    hasUnlimitedLikes: boolean;
    subscription: any;
  };
  resetDailyLimits: () => void;

  // Real-time updates
  addNewLike: (like: LikeProfile) => void;
  addNewMatch: (match: Match) => void;
  updateMatchStatus: (matchId: string, status: Match['status']) => void;

  // Cache and offline support
  loadFromCache: () => Promise<void>;
  syncPendingActions: () => Promise<void>;
  setOnlineStatus: (isOnline: boolean) => void;

  // Utility
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  receivedLikes: [],
  sentLikes: [],
  matches: [],
  isLoading: false,
  isRefreshing: false,
  error: null,
  isOnline: true,
  hasMoreLikes: true,
  hasMoreMatches: true,
  likesPage: 1,
  matchesPage: 1,
  dailyLikesUsed: 0,
  dailySuperLikesUsed: 0,
  lastResetDate: new Date().toDateString(),
};

/**
 * Normalizes dates in LikeProfile objects to handle serialization issues
 */
function normalizeLikeProfile(profile: LikeProfile): LikeProfile {
  return {
    ...profile,
    likedAt: safeParseDate(profile.likedAt) || new Date(),
  };
}

/**
 * Normalizes dates in Match objects to handle serialization issues
 */
function normalizeMatch(match: Match & { otherUser?: any }): Match & { otherUser?: any } {
  return {
    ...match,
    timestamp: safeParseDate(match.timestamp) || new Date(),
    lastActivity: match.lastActivity ? safeParseDate(match.lastActivity) || undefined : undefined,
  };
}

export const useLikesStore = create<LikesState>()(
  persist(
    (set, get) => ({
      ...initialState,

      fetchReceivedLikes: async (refresh = false) => {
        const state = get();

        if (state.isLoading && !refresh) return;

        // Use a try-catch to prevent state updates on unmounted components
        try {
          set({
            isLoading: !refresh,
            isRefreshing: refresh,
            error: null
          });
        } catch (error) {
          console.warn('Failed to update loading state:', error);
          return;
        }

        try {
          const page = refresh ? 1 : state.likesPage;
          const data = await likesService.fetchReceivedLikes(page, 20);

          try {
            // Normalize dates in the received data
            const normalizedLikes = data.likes.map(normalizeLikeProfile);

            set((state) => ({
              receivedLikes: refresh
                ? normalizedLikes
                : [...state.receivedLikes, ...normalizedLikes],
              hasMoreLikes: data.hasMore,
              likesPage: refresh ? 2 : state.likesPage + 1,
              isLoading: false,
              isRefreshing: false,
            }));
          } catch (setError) {
            console.warn('Failed to update likes state:', setError);
          }
        } catch (error) {
          try {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
              isRefreshing: false,
            });
          } catch (setError) {
            console.warn('Failed to update error state:', setError);
          }
        }
      },

      fetchMatches: async (refresh = false) => {
        const state = get();
        
        if (state.isLoading && !refresh) return;
        
        set({ 
          isLoading: !refresh, 
          isRefreshing: refresh,
          error: null 
        });

        try {
          const page = refresh ? 1 : state.matchesPage;
          const data = await likesService.fetchMatches(page, 20);

          // Normalize dates in the received data
          const normalizedMatches = data.matches.map(normalizeMatch);

          set((state) => ({
            matches: refresh
              ? normalizedMatches
              : [...state.matches, ...normalizedMatches],
            hasMoreMatches: data.hasMore,
            matchesPage: refresh ? 2 : state.matchesPage + 1,
            isLoading: false,
            isRefreshing: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false,
            isRefreshing: false,
          });
        }
      },

      sendLike: async (userId: string, type: 'like' | 'superlike') => {
        const state = get();

        // Check daily limits first
        get().resetDailyLimits();

        // Check if user can send this type of like
        const canSend = type === 'like' ? get().canSendLike() : get().canSendSuperLike();
        if (!canSend.canSend) {
          set({ error: canSend.reason || 'Cannot send like' });
          return false;
        }

        try {
          // If offline, add to pending actions
          if (!state.isOnline) {
            await likesCacheService.addPendingAction({
              type,
              userId,
              data: { type }
            });
            console.log('Like added to pending actions (offline)');
            return false; // Can't determine match status offline
          }

          const data = await likesService.sendLike(userId, type);

          // Update usage counters
          if (type === 'like') {
            set((state) => ({ dailyLikesUsed: state.dailyLikesUsed + 1 }));
          } else if (type === 'superlike') {
            set((state) => ({ dailySuperLikesUsed: state.dailySuperLikesUsed + 1 }));
          }

          // Update premium store usage
          const premiumStore = usePremiumStore.getState();
          if (type === 'like') {
            premiumStore.incrementUsage('unlimited_likes');
          } else if (type === 'superlike') {
            premiumStore.incrementUsage('super_likes');
          }

          // Add to sent likes
          set((state) => ({
            sentLikes: [...state.sentLikes, data.like],
          }));

          // Cache the updated data
          const updatedState = get();
          await likesCacheService.cacheLikesData({
            receivedLikes: updatedState.receivedLikes,
            sentLikes: updatedState.sentLikes,
            matches: updatedState.matches,
          });

          // Send real-time update
          const realTimeManager = getRealTimeLikesManager();
          if (realTimeManager) {
            realTimeManager.sendLike(userId, type);
          }

          // Return whether it's a match
          return data.isMatch;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return false;
        }
      },

      sendPass: async (userId: string) => {
        try {
          await likesService.sendPass(userId);
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      },

      likeBack: async (userId: string) => {
        const state = get();

        try {
          // If offline, add to pending actions
          if (!state.isOnline) {
            await likesCacheService.addPendingAction({
              type: 'likeBack',
              userId,
            });
            console.log('Like back added to pending actions (offline)');
            return false; // Can't determine match status offline
          }

          const data = await likesService.likeBack(userId);

          // Update the like to show it's now a match
          set((state) => ({
            receivedLikes: state.receivedLikes.map(like =>
              like.id === userId ? { ...like, isMatch: true } : like
            ),
          }));

          // Add to matches if it's a new match
          if (data.isMatch && data.match) {
            set((state) => ({
              matches: [data.match!, ...state.matches],
            }));

            // Cache the new match
            await likesCacheService.addMatchToCache(data.match);
          }

          // Send real-time update
          const realTimeManager = getRealTimeLikesManager();
          if (realTimeManager) {
            realTimeManager.sendLikeBack(userId);
          }

          return data.isMatch;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return false;
        }
      },

      addNewLike: (like: LikeProfile) => {
        set((state) => ({
          receivedLikes: [like, ...state.receivedLikes],
        }));
      },

      addNewMatch: (match: Match) => {
        set((state) => ({
          matches: [match, ...state.matches],
        }));
      },

      updateMatchStatus: (matchId: string, status: Match['status']) => {
        set((state) => ({
          matches: state.matches.map(match =>
            match.id === matchId ? { ...match, status } : match
          ),
        }));
      },

      // Cache and offline support
      loadFromCache: async () => {
        try {
          const cachedData = await likesCacheService.getCachedLikesData();
          if (cachedData) {
            set({
              receivedLikes: cachedData.receivedLikes.map(normalizeLikeProfile),
              sentLikes: cachedData.sentLikes,
              matches: cachedData.matches.map(normalizeMatch),
            });
            console.log('Loaded likes data from cache');
          }
        } catch (error) {
          console.error('Failed to load from cache:', error);
        }
      },

      syncPendingActions: async () => {
        const state = get();
        if (!state.isOnline) return;

        try {
          const result = await likesCacheService.syncPendingActions(async (action) => {
            try {
              switch (action.type) {
                case 'like':
                case 'superlike':
                  await likesService.sendLike(action.userId, action.type);
                  break;
                case 'pass':
                  await likesService.sendPass(action.userId);
                  break;
                case 'likeBack':
                  await likesService.likeBack(action.userId);
                  break;
              }
              return true;
            } catch (error) {
              console.error('Failed to sync action:', action.type, error);
              return false;
            }
          });

          console.log(`Sync completed: ${result.synced} synced, ${result.failed} failed`);
        } catch (error) {
          console.error('Failed to sync pending actions:', error);
        }
      },

      setOnlineStatus: (isOnline: boolean) => {
        set({ isOnline });
        if (isOnline) {
          // Sync pending actions when coming back online
          get().syncPendingActions();
        }
      },

      // Premium feature checks
      canSendLike: () => {
        const state = get();
        const premiumStore = usePremiumStore.getState();

        // Premium users have unlimited likes
        if (premiumStore.hasFeature('unlimited_likes')) {
          return { canSend: true };
        }

        // Check daily limit for free users
        if (state.dailyLikesUsed >= FREE_USER_LIMITS.daily_likes) {
          return {
            canSend: false,
            reason: `You've reached your daily limit of ${FREE_USER_LIMITS.daily_likes} likes. Upgrade to Premium for unlimited likes!`
          };
        }

        return { canSend: true };
      },

      canSendSuperLike: () => {
        const state = get();
        const premiumStore = usePremiumStore.getState();

        // Premium users get more super likes
        const limit = premiumStore.hasFeature('super_likes') ? 5 : FREE_USER_LIMITS.super_likes;

        if (state.dailySuperLikesUsed >= limit) {
          return {
            canSend: false,
            reason: premiumStore.hasFeature('super_likes')
              ? `You've used all ${limit} Super Likes today. They reset at midnight!`
              : `You've used your ${limit} daily Super Like. Upgrade to Premium for 5 daily Super Likes!`
          };
        }

        return { canSend: true };
      },

      canViewWhoLikesYou: () => {
        const premiumStore = usePremiumStore.getState();
        return premiumStore.hasFeature('see_who_likes_you') || premiumStore.isPremium;
      },

      // Get premium status for UI display
      getPremiumStatus: () => {
        const premiumStore = usePremiumStore.getState();
        return {
          isPremium: premiumStore.isPremium,
          canViewLikes: premiumStore.hasFeature('see_who_likes_you') || premiumStore.isPremium,
          hasUnlimitedLikes: premiumStore.hasFeature('unlimited_likes') || premiumStore.isPremium,
          subscription: premiumStore.subscription,
        };
      },

      resetDailyLimits: () => {
        const state = get();
        const today = new Date().toDateString();

        if (state.lastResetDate !== today) {
          set({
            dailyLikesUsed: 0,
            dailySuperLikesUsed: 0,
            lastResetDate: today,
          });
        }
      },

      clearError: () => set({ error: null }),

      reset: () => set(initialState),
    }),
    {
      name: 'likes-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        receivedLikes: state.receivedLikes,
        sentLikes: state.sentLikes,
        matches: state.matches,
        dailyLikesUsed: state.dailyLikesUsed,
        dailySuperLikesUsed: state.dailySuperLikesUsed,
        lastResetDate: state.lastResetDate,
      }),
      onRehydrateStorage: () => (state) => {
        // Normalize dates when rehydrating from storage
        if (state) {
          state.receivedLikes = state.receivedLikes.map(normalizeLikeProfile);
          state.matches = state.matches.map(normalizeMatch);
        }
      },
    }
  )
);
