import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  Platform,
  ActionSheetIOS,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import PhotoEditor from '@/components/profile/PhotoEditor';
import PhotoLoadingSkeleton, { PhotoUploadSkeleton, PhotoVerificationSkeleton } from '@/components/ui/PhotoLoadingSkeleton';
import {
  ArrowLeft,
  Plus,
  Camera,
  Image as ImageIcon,
  Star,
  Trash2,
  Move,
  Edit3,
  Shield,
  CheckCircle,
  Clock,
  AlertCircle,
} from 'lucide-react-native';

export default function PhotoManagementScreen() {
  const router = useRouter();
  const {
    profile,
    uploadPhoto,
    deletePhoto,
    setMainPhoto,
    reorderPhotos,
    isUploadingPhoto,
    isLoading
  } = useProfileStore();

  const [isReordering, setIsReordering] = useState(false);
  const [showPhotoEditor, setShowPhotoEditor] = useState(false);
  const [editingPhotoUri, setEditingPhotoUri] = useState<string>('');
  const [deletingPhotoId, setDeletingPhotoId] = useState<string | null>(null);
  const [photoVerificationStatus, setPhotoVerificationStatus] = useState<{
    [key: string]: 'pending' | 'verified' | 'rejected' | 'none';
  }>({});

  const requestPermissions = async () => {
    try {
      // On web, permissions are handled differently
      if (Platform.OS === 'web') {
        return true; // Web browsers handle permissions automatically
      }

      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      console.log('Camera permission:', cameraPermission.status);
      console.log('Gallery permission:', galleryPermission.status);

      return cameraPermission.status === 'granted' && galleryPermission.status === 'granted';
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  };

  const showImagePicker = () => {
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
          cancelButtonIndex: 0,
        },
        (buttonIndex) => {
          if (buttonIndex === 1) {
            takePhoto();
          } else if (buttonIndex === 2) {
            pickImage();
          }
        }
      );
    } else {
      Alert.alert(
        'Add Photo',
        'Choose an option',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Take Photo', onPress: takePhoto },
          { text: 'Choose from Gallery', onPress: pickImage },
        ]
      );
    }
  };

  const takePhoto = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Camera and gallery permissions are required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 5],
        quality: 0.9,
      });

      if (!result.canceled && result.assets[0]) {
        await handlePhotoUpload(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const pickImage = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Gallery permission is required to select photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 5],
        quality: 0.9,
      });

      if (!result.canceled && result.assets[0]) {
        await handlePhotoUpload(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select photo. Please try again.');
    }
  };

  const handlePhotoUpload = async (uri: string) => {
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please refresh the app and try again.');
      return;
    }

    if (profile.photos.length >= 9) {
      Alert.alert('Limit Reached', 'You can upload up to 9 photos. Please delete some photos first.');
      return;
    }

    // Validate image URI
    if (!uri || uri.trim() === '') {
      Alert.alert('Error', 'Invalid image selected. Please try again.');
      return;
    }

    try {
      // Generate consistent photo ID
      const photoId = `photo-${Date.now()}`;

      console.log('Starting photo upload with ID:', photoId, 'URI:', uri);

      // Check if we're already uploading
      if (isUploadingPhoto) {
        Alert.alert('Upload in Progress', 'Please wait for the current upload to complete.');
        return;
      }

      const uploadResult = await uploadPhoto({
        uri,
        type: 'image/jpeg',
        name: `${photoId}.jpg`,
        isMain: profile.photos.length === 0,
        order: profile.photos.length,
        id: photoId, // Ensure consistent ID
      });

      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      // Set initial verification status with the same ID
      setPhotoVerificationStatus(prev => ({
        ...prev,
        [photoId]: 'none'
      }));

      console.log('Photo upload successful:', uploadResult);
      Alert.alert('Success', 'Photo uploaded successfully!');
    } catch (error) {
      console.error('Photo upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Provide specific error messages based on error type
      let userMessage = 'Failed to upload photo. Please try again.';
      if (errorMessage.includes('network') || errorMessage.includes('connection')) {
        userMessage = 'Network error. Please check your internet connection and try again.';
      } else if (errorMessage.includes('permission')) {
        userMessage = 'Permission denied. Please check app permissions and try again.';
      } else if (errorMessage.includes('duplicate')) {
        userMessage = 'This photo has already been uploaded.';
      }

      Alert.alert('Upload Failed', `${userMessage}\n\nError details: ${errorMessage}`);
    }
  };

  const handleEditPhoto = (photoUri: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setEditingPhotoUri(photoUri);
    setShowPhotoEditor(true);
  };

  const handlePhotoEdited = async (editedUri: string) => {
    // In production, you would update the photo with the edited version
    console.log('Photo edited:', editedUri);
    setShowPhotoEditor(false);
    setEditingPhotoUri('');
  };

  const handleVerifyPhoto = async (photoId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    Alert.alert(
      'Verify Photo',
      'Submit this photo for verification? Our team will review it within 24 hours.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Submit',
          onPress: async () => {
            setPhotoVerificationStatus(prev => ({
              ...prev,
              [photoId]: 'pending'
            }));

            // Simulate verification process
            setTimeout(() => {
              setPhotoVerificationStatus(prev => ({
                ...prev,
                [photoId]: 'verified'
              }));
              Alert.alert('Verified!', 'Your photo has been verified successfully.');
            }, 3000);
          }
        }
      ]
    );
  };

  const handleDeletePhoto = (photoId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('Deleting photo with ID:', photoId);

              // Set loading state
              setDeletingPhotoId(photoId);

              // Show loading state
              const photoToDelete = profile?.photos.find(p => p.id === photoId);
              if (!photoToDelete) {
                Alert.alert('Error', 'Photo not found. Please refresh and try again.');
                setDeletingPhotoId(null);
                return;
              }

              await deletePhoto(photoId);

              // Remove from local verification status
              setPhotoVerificationStatus(prev => {
                const updated = { ...prev };
                delete updated[photoId];
                return updated;
              });

              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              }

              console.log('Photo deleted successfully');
            } catch (error) {
              console.error('Photo deletion error:', error);
              const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
              Alert.alert('Deletion Failed', `Failed to delete photo: ${errorMessage}\n\nPlease try again.`);
            } finally {
              setDeletingPhotoId(null);
            }
          },
        },
      ]
    );
  };

  const handleSetMainPhoto = async (photoId: string) => {
    try {
      await setMainPhoto(photoId);
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
      Alert.alert('Success', 'Main photo updated!');
    } catch (error) {
      Alert.alert('Error', 'Failed to set main photo. Please try again.');
    }
  };

  const PhotoSlot = ({ photo, index }: { photo?: any; index: number }) => {
    const isEmpty = !photo;
    const isMain = photo?.isMain;
    const isDeleting = photo && deletingPhotoId === photo.id;
    const verificationStatus = photo ? (photo.verificationStatus || photoVerificationStatus[photo.id] || 'none') : 'none';

    const getVerificationIcon = () => {
      switch (verificationStatus) {
        case 'verified':
          return <CheckCircle size={14} color={theme.colors.success} />;
        case 'pending':
          return <Clock size={14} color={theme.colors.warning} />;
        case 'rejected':
          return <AlertCircle size={14} color={theme.colors.error} />;
        default:
          return null;
      }
    };

    return (
      <View style={styles.photoSlot}>
        {isEmpty ? (
          isUploadingPhoto ? (
            <PhotoUploadSkeleton />
          ) : (
            <TouchableOpacity
              style={styles.emptySlot}
              onPress={showImagePicker}
              disabled={isUploadingPhoto}
            >
              <Plus size={32} color={theme.colors.gray400} />
              <Text style={styles.emptySlotText}>Add Photo</Text>
            </TouchableOpacity>
          )
        ) : (
          <View style={styles.photoContainer}>
            <Image source={{ uri: photo.url }} style={styles.photo} />

            {/* Loading overlay for deletion */}
            {isDeleting && (
              <View style={styles.loadingOverlay}>
                <View style={styles.loadingSpinner}>
                  <Text style={styles.loadingText}>Deleting...</Text>
                </View>
              </View>
            )}

            {isMain && (
              <View style={styles.mainBadge}>
                <Star size={16} color="white" fill="white" />
                <Text style={styles.mainBadgeText}>Main</Text>
              </View>
            )}

            {/* Verification Status */}
            {verificationStatus !== 'none' && (
              <View style={[styles.verificationBadge, styles[`verification${verificationStatus.charAt(0).toUpperCase() + verificationStatus.slice(1)}`]]}>
                {getVerificationIcon()}
              </View>
            )}

            <View style={styles.photoActions}>
              <TouchableOpacity
                style={[styles.actionButton, isDeleting && styles.actionButtonDisabled]}
                onPress={() => !isDeleting && handleEditPhoto(photo.url)}
                disabled={isDeleting}
              >
                <Edit3 size={16} color="white" />
              </TouchableOpacity>

              {!isMain && (
                <TouchableOpacity
                  style={[styles.actionButton, isDeleting && styles.actionButtonDisabled]}
                  onPress={() => !isDeleting && handleSetMainPhoto(photo.id)}
                  disabled={isDeleting}
                >
                  <Star size={16} color="white" />
                </TouchableOpacity>
              )}

              {verificationStatus === 'none' && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.verifyButton, isDeleting && styles.actionButtonDisabled]}
                  onPress={() => !isDeleting && handleVerifyPhoto(photo.id)}
                  disabled={isDeleting}
                >
                  <Shield size={16} color="white" />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton, isDeleting && styles.actionButtonDisabled]}
                onPress={() => !isDeleting && handleDeletePhoto(photo.id)}
                disabled={isDeleting}
              >
                <Trash2 size={16} color="white" />
              </TouchableOpacity>
            </View>

            {index === 0 && (
              <View style={styles.orderBadge}>
                <Text style={styles.orderText}>1</Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Manage Photos</Text>
          <View style={styles.backButton} />
        </View>
        <PhotoLoadingSkeleton count={9} showGrid={true} />
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load profile</Text>
        </View>
      </SafeAreaView>
    );
  }

  const sortedPhotos = [...profile.photos].sort((a, b) => a.order - b.order);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Manage Photos</Text>
        <TouchableOpacity 
          style={styles.reorderButton}
          onPress={() => setIsReordering(!isReordering)}
        >
          <Move size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Photo Guidelines</Text>
          <Text style={styles.infoText}>
            • Upload 2-9 high-quality photos{'\n'}
            • First photo will be your main profile photo{'\n'}
            • Show your face clearly in at least one photo{'\n'}
            • Avoid group photos as your main photo{'\n'}
            • Keep photos recent and authentic
          </Text>
        </View>

        {/* Photo Grid */}
        <View style={styles.photoGrid}>
          {Array.from({ length: 9 }, (_, index) => {
            const photo = sortedPhotos[index];
            return (
              <PhotoSlot 
                key={index} 
                photo={photo} 
                index={index}
              />
            );
          })}
        </View>

        {/* Upload Button */}
        {profile.photos.length < 9 && (
          <TouchableOpacity 
            style={[styles.uploadButton, isUploadingPhoto && styles.uploadButtonDisabled]}
            onPress={showImagePicker}
            disabled={isUploadingPhoto}
          >
            <Camera size={20} color="white" />
            <Text style={styles.uploadButtonText}>
              {isUploadingPhoto ? 'Uploading...' : 'Add More Photos'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Photo Count */}
        <View style={styles.photoCount}>
          <Text style={styles.photoCountText}>
            {profile.photos.length} of 9 photos
          </Text>
        </View>

        {/* Photo Verification Status */}
        {Object.keys(photoVerificationStatus).length > 0 && (
          <View style={styles.verificationSection}>
            <Text style={styles.verificationTitle}>Photo Verification Status</Text>
            {Object.entries(photoVerificationStatus).map(([photoId, status]) => (
              <PhotoVerificationSkeleton key={photoId} />
            ))}
          </View>
        )}
      </ScrollView>

      {/* Photo Editor Modal */}
      <PhotoEditor
        visible={showPhotoEditor}
        onClose={() => setShowPhotoEditor(false)}
        imageUri={editingPhotoUri}
        onSave={handlePhotoEdited}
        aspectRatio={[4, 5]}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  reorderButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.error,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  infoTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 10,
  },
  photoSlot: {
    width: '31%',
    aspectRatio: 4/5,
    marginBottom: 10,
  },
  emptySlot: {
    flex: 1,
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.gray300,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptySlotText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray500,
    marginTop: 4,
  },
  photoContainer: {
    flex: 1,
    position: 'relative',
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  mainBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  mainBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  photoActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    gap: 4,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  verifyButton: {
    backgroundColor: 'rgba(139, 92, 246, 0.8)',
  },
  verificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationVerified: {
    backgroundColor: 'rgba(34, 197, 94, 0.9)',
  },
  verificationPending: {
    backgroundColor: 'rgba(251, 191, 36, 0.9)',
  },
  verificationRejected: {
    backgroundColor: 'rgba(239, 68, 68, 0.9)',
  },
  verificationSection: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  verificationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  orderBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  orderText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    marginHorizontal: 20,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  photoCount: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  photoCountText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  loadingSpinner: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  loadingText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
});
