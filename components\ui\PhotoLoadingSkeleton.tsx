import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { theme } from '@/constants/theme';

const { width: screenWidth } = Dimensions.get('window');

interface PhotoLoadingSkeletonProps {
  count?: number;
  aspectRatio?: [number, number];
  showGrid?: boolean;
}

export default function PhotoLoadingSkeleton({
  count = 9,
  aspectRatio = [4, 5],
  showGrid = true,
}: PhotoLoadingSkeletonProps) {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1500 }),
      -1,
      true
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmerValue.value,
      [0, 1],
      [-100, 100]
    );

    return {
      transform: [{ translateX }],
    };
  });

  const photoWidth = showGrid 
    ? (screenWidth - 60) / 3 
    : screenWidth - 40;
  const photoHeight = photoWidth * (aspectRatio[1] / aspectRatio[0]);

  const SkeletonPhoto = ({ index }: { index: number }) => (
    <View 
      style={[
        styles.photoSkeleton, 
        { 
          width: photoWidth, 
          height: photoHeight,
          marginBottom: showGrid ? 10 : 20,
          marginRight: showGrid && (index + 1) % 3 !== 0 ? 10 : 0,
        }
      ]}
    >
      <Animated.View style={[styles.shimmer, shimmerStyle]} />
      
      {/* Photo placeholder icon */}
      <View style={styles.photoIcon}>
        <View style={styles.iconCircle} />
        <View style={styles.iconLines}>
          <View style={styles.iconLine} />
          <View style={[styles.iconLine, styles.iconLineShort]} />
        </View>
      </View>

      {/* Action buttons skeleton */}
      <View style={styles.actionsSkeleton}>
        <View style={styles.actionButtonSkeleton} />
        <View style={styles.actionButtonSkeleton} />
      </View>

      {/* Main badge skeleton for first photo */}
      {index === 0 && (
        <View style={styles.mainBadgeSkeleton}>
          <View style={styles.starSkeleton} />
          <View style={styles.mainTextSkeleton} />
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {showGrid ? (
        <View style={styles.gridContainer}>
          {Array.from({ length: count }, (_, index) => (
            <SkeletonPhoto key={index} index={index} />
          ))}
        </View>
      ) : (
        <View style={styles.listContainer}>
          {Array.from({ length: count }, (_, index) => (
            <SkeletonPhoto key={index} index={index} />
          ))}
        </View>
      )}
    </View>
  );
}

export function PhotoUploadSkeleton() {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1200 }),
      -1,
      true
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      shimmerValue.value,
      [0, 0.5, 1],
      [0.3, 0.7, 0.3]
    );

    return { opacity };
  });

  return (
    <View style={styles.uploadContainer}>
      <Animated.View style={[styles.uploadSkeleton, shimmerStyle]}>
        <View style={styles.uploadIcon}>
          <View style={styles.uploadIconCircle} />
          <View style={styles.uploadIconPlus} />
        </View>
        <View style={styles.uploadTextSkeleton} />
        <View style={styles.uploadProgressSkeleton} />
      </Animated.View>
    </View>
  );
}

export function PhotoVerificationSkeleton() {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1000 }),
      -1,
      true
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      shimmerValue.value,
      [0, 1],
      [0.4, 0.8]
    );

    return { opacity };
  });

  return (
    <View style={styles.verificationContainer}>
      <Animated.View style={[styles.verificationSkeleton, shimmerStyle]}>
        <View style={styles.verificationIcon} />
        <View style={styles.verificationTextContainer}>
          <View style={styles.verificationTitle} />
          <View style={styles.verificationSubtitle} />
        </View>
        <View style={styles.verificationStatus} />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  photoSkeleton: {
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    width: '100%',
  },
  photoIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
    alignItems: 'center',
  },
  iconCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.gray300,
    marginBottom: 4,
  },
  iconLines: {
    alignItems: 'center',
  },
  iconLine: {
    width: 16,
    height: 2,
    backgroundColor: theme.colors.gray300,
    borderRadius: 1,
    marginBottom: 2,
  },
  iconLineShort: {
    width: 12,
  },
  actionsSkeleton: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
  },
  actionButtonSkeleton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: theme.colors.gray300,
    marginLeft: 4,
  },
  mainBadgeSkeleton: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray300,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  starSkeleton: {
    width: 12,
    height: 12,
    backgroundColor: theme.colors.gray400,
    borderRadius: 2,
    marginRight: 4,
  },
  mainTextSkeleton: {
    width: 24,
    height: 8,
    backgroundColor: theme.colors.gray400,
    borderRadius: 4,
  },
  uploadContainer: {
    padding: 20,
  },
  uploadSkeleton: {
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.gray200,
    borderStyle: 'dashed',
  },
  uploadIcon: {
    alignItems: 'center',
    marginBottom: 12,
  },
  uploadIconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray300,
    marginBottom: 8,
  },
  uploadIconPlus: {
    width: 20,
    height: 2,
    backgroundColor: theme.colors.gray400,
    borderRadius: 1,
  },
  uploadTextSkeleton: {
    width: 120,
    height: 12,
    backgroundColor: theme.colors.gray300,
    borderRadius: 6,
    marginBottom: 16,
  },
  uploadProgressSkeleton: {
    width: 200,
    height: 4,
    backgroundColor: theme.colors.gray200,
    borderRadius: 2,
  },
  verificationContainer: {
    padding: 20,
  },
  verificationSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray100,
    padding: 16,
    borderRadius: 12,
  },
  verificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray300,
    marginRight: 12,
  },
  verificationTextContainer: {
    flex: 1,
  },
  verificationTitle: {
    width: 120,
    height: 14,
    backgroundColor: theme.colors.gray300,
    borderRadius: 7,
    marginBottom: 6,
  },
  verificationSubtitle: {
    width: 80,
    height: 10,
    backgroundColor: theme.colors.gray200,
    borderRadius: 5,
  },
  verificationStatus: {
    width: 60,
    height: 24,
    backgroundColor: theme.colors.gray300,
    borderRadius: 12,
  },
});
