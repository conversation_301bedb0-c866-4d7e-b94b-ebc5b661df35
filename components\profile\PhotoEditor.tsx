import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  Image,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import { PanGestureHandler, PinchGestureHandler } from 'react-native-gesture-handler';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import {
  X,
  Check,
  RotateCw,
  Crop,
  Sliders,
  Download,
  Loader,
} from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PhotoEditorProps {
  visible: boolean;
  onClose: () => void;
  imageUri: string;
  onSave: (editedUri: string) => void;
  aspectRatio?: [number, number];
}

interface EditingState {
  brightness: number;
  contrast: number;
  saturation: number;
  rotation: number;
  cropArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export default function PhotoEditor({
  visible,
  onClose,
  imageUri,
  onSave,
  aspectRatio = [4, 5],
}: PhotoEditorProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [editMode, setEditMode] = useState<'crop' | 'adjust' | 'rotate'>('crop');
  const [editingState, setEditingState] = useState<EditingState>({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    rotation: 0,
    cropArea: {
      x: 0,
      y: 0,
      width: screenWidth - 40,
      height: (screenWidth - 40) * (aspectRatio[1] / aspectRatio[0]),
    },
  });

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotation = useSharedValue(0);

  const triggerHaptic = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleClose = () => {
    triggerHaptic();
    onClose();
  };

  const handleRotate = () => {
    triggerHaptic();
    const newRotation = (editingState.rotation + 90) % 360;
    setEditingState(prev => ({ ...prev, rotation: newRotation }));
    rotation.value = withSpring(newRotation);
  };

  const handleModeChange = (mode: 'crop' | 'adjust' | 'rotate') => {
    triggerHaptic();
    setEditMode(mode);
  };

  const handleSave = async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setIsProcessing(true);

    try {
      // In production, you would use expo-image-manipulator or similar
      // to apply the actual edits to the image
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      // For demo purposes, return the original URI
      // In production, this would be the processed image URI
      const processedUri = imageUri;
      
      onSave(processedUri);
      Alert.alert('Success', 'Photo edited successfully!');
      onClose();
    } catch (error) {
      console.error('Photo editing error:', error);
      Alert.alert('Error', 'Failed to process photo. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Gesture handlers
  const pinchHandler = useAnimatedGestureHandler({
    onStart: () => {
      runOnJS(triggerHaptic)();
    },
    onActive: (event) => {
      scale.value = Math.max(0.5, Math.min(3, event.scale));
    },
    onEnd: () => {
      if (scale.value < 1) {
        scale.value = withSpring(1);
      }
    },
  });

  const panHandler = useAnimatedGestureHandler({
    onStart: () => {
      runOnJS(triggerHaptic)();
    },
    onActive: (event) => {
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    },
    onEnd: () => {
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { translateX: translateX.value },
        { translateY: translateY.value },
        { rotate: `${rotation.value}deg` },
      ],
    };
  });

  const renderEditingControls = () => {
    switch (editMode) {
      case 'crop':
        return (
          <View style={styles.controlsContainer}>
            <Text style={styles.controlsTitle}>Crop & Position</Text>
            <Text style={styles.controlsSubtitle}>
              Pinch to zoom, drag to reposition
            </Text>
          </View>
        );
      case 'adjust':
        return (
          <View style={styles.controlsContainer}>
            <Text style={styles.controlsTitle}>Adjust</Text>
            <Text style={styles.controlsSubtitle}>
              Brightness, contrast, and saturation controls would go here
            </Text>
          </View>
        );
      case 'rotate':
        return (
          <View style={styles.controlsContainer}>
            <Text style={styles.controlsTitle}>Rotate</Text>
            <TouchableOpacity style={styles.rotateButton} onPress={handleRotate}>
              <RotateCw size={24} color="white" />
              <Text style={styles.rotateButtonText}>Rotate 90°</Text>
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} animationType="slide" statusBarTranslucent>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={handleClose}>
            <X size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Photo</Text>
          <TouchableOpacity 
            style={[styles.headerButton, isProcessing && styles.headerButtonDisabled]} 
            onPress={handleSave}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <Loader size={24} color="white" />
            ) : (
              <Check size={24} color="white" />
            )}
          </TouchableOpacity>
        </View>

        {/* Image Editor */}
        <View style={styles.imageContainer}>
          <View style={styles.cropArea}>
            <PinchGestureHandler onGestureEvent={pinchHandler}>
              <Animated.View>
                <PanGestureHandler onGestureEvent={panHandler}>
                  <Animated.View style={animatedStyle}>
                    <Image source={{ uri: imageUri }} style={styles.image} />
                  </Animated.View>
                </PanGestureHandler>
              </Animated.View>
            </PinchGestureHandler>
          </View>
          
          {/* Crop overlay */}
          {editMode === 'crop' && (
            <View style={styles.cropOverlay}>
              <View style={styles.cropGrid}>
                <View style={styles.gridLine} />
                <View style={[styles.gridLine, styles.gridLineVertical]} />
                <View style={[styles.gridLine, styles.gridLineHorizontal]} />
                <View style={[styles.gridLine, styles.gridLineVertical, styles.gridLineRight]} />
              </View>
            </View>
          )}
        </View>

        {/* Mode Selector */}
        <View style={styles.modeSelector}>
          <TouchableOpacity
            style={[styles.modeButton, editMode === 'crop' && styles.modeButtonActive]}
            onPress={() => handleModeChange('crop')}
          >
            <Crop size={20} color={editMode === 'crop' ? theme.colors.primary : 'white'} />
            <Text style={[styles.modeButtonText, editMode === 'crop' && styles.modeButtonTextActive]}>
              Crop
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.modeButton, editMode === 'adjust' && styles.modeButtonActive]}
            onPress={() => handleModeChange('adjust')}
          >
            <Sliders size={20} color={editMode === 'adjust' ? theme.colors.primary : 'white'} />
            <Text style={[styles.modeButtonText, editMode === 'adjust' && styles.modeButtonTextActive]}>
              Adjust
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.modeButton, editMode === 'rotate' && styles.modeButtonActive]}
            onPress={() => handleModeChange('rotate')}
          >
            <RotateCw size={20} color={editMode === 'rotate' ? theme.colors.primary : 'white'} />
            <Text style={[styles.modeButtonText, editMode === 'rotate' && styles.modeButtonTextActive]}>
              Rotate
            </Text>
          </TouchableOpacity>
        </View>

        {/* Editing Controls */}
        {renderEditingControls()}
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerButton: {
    padding: 8,
  },
  headerButtonDisabled: {
    opacity: 0.5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  cropArea: {
    width: screenWidth - 40,
    height: (screenWidth - 40) * (5 / 4),
    overflow: 'hidden',
    borderRadius: 12,
  },
  image: {
    width: screenWidth - 40,
    height: (screenWidth - 40) * (5 / 4),
    resizeMode: 'cover',
  },
  cropOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: screenWidth - 40,
    height: (screenWidth - 40) * (5 / 4),
    marginLeft: -(screenWidth - 40) / 2,
    marginTop: -((screenWidth - 40) * (5 / 4)) / 2,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 12,
  },
  cropGrid: {
    flex: 1,
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  gridLineVertical: {
    width: 1,
    height: '100%',
    left: '33.33%',
  },
  gridLineHorizontal: {
    height: 1,
    width: '100%',
    top: '33.33%',
  },
  gridLineRight: {
    left: '66.66%',
  },
  modeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  modeButton: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    minWidth: 60,
  },
  modeButtonActive: {
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
  },
  modeButtonText: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
  },
  modeButtonTextActive: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  controlsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  controlsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 4,
  },
  controlsSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  rotateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    marginTop: 15,
  },
  rotateButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
});
